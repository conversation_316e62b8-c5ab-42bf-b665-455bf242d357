<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.effect.DropShadow?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
    <BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0">
        <top>
            <VBox style="-fx-background-color: white; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);" BorderPane.alignment="CENTER">
                <children>
                    <HBox alignment="CENTER" prefHeight="100.0" style="-fx-padding: 15;">
                        <children>
                            <ImageView fx:id="aimsImage" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
                                <image>
                                    <Image url="@../assets/images/Logo.png" />
                                </image>
                                <cursor>
                                    <Cursor fx:constant="HAND" />
                                </cursor>
                                <HBox.margin>
                                    <Insets right="20.0" />
                                </HBox.margin>
                            </ImageView>
                            <Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="60.0" prefWidth="2.0" style="-fx-background-color: #dee2e6;" />
                            <VBox alignment="CENTER_LEFT" spacing="5.0">
                                <children>
                                    <Label fx:id="pageTitle" text="RUSH DELIVERY INVOICE" textFill="#dc3545">
                                        <font>
                                            <Font name="System Bold" size="28.0" />
                                        </font>
                                    </Label>
                                    <Label text="Express Order Summary & Payment" textFill="#6c757d">
                                        <font>
                                            <Font size="16.0" />
                                        </font>
                                    </Label>
                                </children>
                                <HBox.margin>
                                    <Insets left="20.0" />
                                </HBox.margin>
                            </VBox>
                            <Region HBox.hgrow="ALWAYS" />
                        </children>
                    </HBox>
                </children>
            </VBox>
        </top>
        <center>
            <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;" BorderPane.alignment="CENTER">
                <content>
                    <HBox prefWidth="1326.0" spacing="20.0" style="-fx-padding: 30;">
                        <children>
                            <!-- Main Invoice Content -->
                            <VBox fx:id="coverVBox" prefWidth="800.0" spacing="25.0">
                                <children>
                                    <!-- Delivery Information Card -->
                                    <VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 25; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
                                        <children>
                                            <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                <children>
                                                    <Label text="📍" textFill="#dc3545">
                                                        <font>
                                                            <Font size="24.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="Delivery Information" textFill="#2c3e50">
                                                        <font>
                                                            <Font name="System Bold" size="22.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </HBox>
                                            <Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

                                            <GridPane hgap="15.0" vgap="12.0">
                                                <columnConstraints>
                                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="120.0" prefWidth="120.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="400.0" prefWidth="500.0" />
                                                </columnConstraints>
                                                <rowConstraints>
                                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
                                                </rowConstraints>
                                                <children>
                                                    <Label text="Recipient:" textFill="#6c757d" GridPane.rowIndex="0">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="recipientNameField" text="Alice" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>

                                                    <Label text="Phone:" textFill="#6c757d" GridPane.rowIndex="1">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="phoneField" text="0123456789" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>

                                                    <Label text="Email:" textFill="#6c757d" GridPane.rowIndex="2">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="emailField" text="<EMAIL>" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>

                                                    <Label text="Address:" textFill="#6c757d" GridPane.rowIndex="3">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="addressField" text="1 Đại Cồ Việt, Hà Nội" textFill="#2c3e50" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </GridPane>
                                        </children>
                                    </VBox>

                                    <!-- Order Items Table -->
                                    <VBox spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 25; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
                                        <children>
                                            <HBox alignment="CENTER_LEFT" spacing="10.0">
                                                <children>
                                                    <Label text="🛒" textFill="#28a745">
                                                        <font>
                                                            <Font size="24.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="Order Items" textFill="#2c3e50">
                                                        <font>
                                                            <Font name="System Bold" size="22.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </HBox>
                                            <Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

                                            <TableView fx:id="orderItemsTable" prefHeight="200.0" style="-fx-background-color: transparent; -fx-border-color: #e9ecef; -fx-border-radius: 8;">
                                                <columns>
                                                    <TableColumn fx:id="col_item_id" prefWidth="60.0" text="ID" style="-fx-alignment: CENTER;" />
                                                    <TableColumn fx:id="col_item_title" prefWidth="300.0" text="Product Title" />
                                                    <TableColumn fx:id="col_item_price" prefWidth="120.0" text="Unit Price" style="-fx-alignment: CENTER;" />
                                                    <TableColumn fx:id="col_item_quantity" prefWidth="80.0" text="Qty" style="-fx-alignment: CENTER;" />
                                                    <TableColumn fx:id="col_item_total" prefWidth="120.0" text="Total" style="-fx-alignment: CENTER;" />
                                                </columns>
                                            </TableView>
                                        </children>
                                    </VBox>
                                </children>
                            </VBox>

                            <!-- Payment Information Sidebar -->
                            <VBox fx:id="paymentInfoVBox" prefWidth="400.0" spacing="20.0" style="-fx-background-color: linear-gradient(to bottom, #dc3545, #c82333); -fx-background-radius: 15; -fx-padding: 25; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 15, 0, 0, 5);">
                                <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                        <children>
                                            <Label text="💳" textFill="white">
                                                <font>
                                                    <Font size="24.0" />
                                                </font>
                                            </Label>
                                            <Label text="Payment Summary" textFill="white">
                                                <font>
                                                    <Font name="System Bold" size="22.0" />
                                                </font>
                                            </Label>
                                        </children>
                                    </HBox>
                                    <Separator prefWidth="200.0" style="-fx-background-color: rgba(255,255,255,0.3);" />
                                    <!-- Rush Delivery Section -->
                                    <VBox fx:id="rushDeliveryVBox" spacing="15.0" style="-fx-background-color: rgba(255,255,255,0.1); -fx-background-radius: 10; -fx-padding: 15;">
                                        <children>
                                            <Label text="⚡ Rush Delivery" textFill="white">
                                                <font>
                                                    <Font name="System Bold" size="18.0" />
                                                </font>
                                            </Label>

                                            <GridPane hgap="10.0" vgap="8.0">
                                                <columnConstraints>
                                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="150.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="100.0" prefWidth="150.0" />
                                                </columnConstraints>
                                                <rowConstraints>
                                                    <RowConstraints minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                                </rowConstraints>
                                                <children>
                                                    <Label text="Subtotal:" textFill="white" GridPane.rowIndex="0">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="rushDeliverySubtotalLabel" text="100,000 VND" textFill="white" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>

                                                    <Label text="VAT (10%):" textFill="white" GridPane.rowIndex="1">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="rushDeliveryVatLabel" text="10,000 VND" textFill="white" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>

                                                    <Label text="Rush Fee:" textFill="white" GridPane.rowIndex="2">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="rushDeliveryShipFeeLabel" text="50,000 VND" textFill="white" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </GridPane>
                                        </children>
                                    </VBox>
                                    <!-- Regular Delivery Section -->
                                    <VBox fx:id="regularDeliveryVBox" spacing="15.0" style="-fx-background-color: rgba(255,255,255,0.1); -fx-background-radius: 10; -fx-padding: 15;">
                                        <children>
                                            <Label text="🚚 Regular Delivery" textFill="white">
                                                <font>
                                                    <Font name="System Bold" size="18.0" />
                                                </font>
                                            </Label>

                                            <GridPane hgap="10.0" vgap="8.0">
                                                <columnConstraints>
                                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="150.0" />
                                                    <ColumnConstraints hgrow="SOMETIMES" minWidth="100.0" prefWidth="150.0" />
                                                </columnConstraints>
                                                <rowConstraints>
                                                    <RowConstraints minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                                    <RowConstraints minHeight="25.0" prefHeight="25.0" vgrow="SOMETIMES" />
                                                </rowConstraints>
                                                <children>
                                                    <Label text="Subtotal:" textFill="white" GridPane.rowIndex="0">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="regularDeliverySubtotalLabel" text="100,000 VND" textFill="white" GridPane.columnIndex="1" GridPane.rowIndex="0">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>

                                                    <Label text="VAT (10%):" textFill="white" GridPane.rowIndex="1">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="regularDeliveryVatLabel" text="10,000 VND" textFill="white" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>

                                                    <Label text="Shipping Fee:" textFill="white" GridPane.rowIndex="2">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label fx:id="regularDeliveryShipFeeLabel" text="25,000 VND" textFill="white" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                                        <font>
                                                            <Font size="14.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </GridPane>
                                        </children>
                                    </VBox>

                                    <Separator prefWidth="200.0" style="-fx-background-color: rgba(255,255,255,0.3);" />
                                    <!-- Total Price Section -->
                                    <VBox spacing="15.0" style="-fx-background-color: rgba(255,255,255,0.2); -fx-background-radius: 10; -fx-padding: 20;">
                                        <children>
                                            <HBox alignment="CENTER" spacing="10.0">
                                                <children>
                                                    <Label text="💰" textFill="white">
                                                        <font>
                                                            <Font size="20.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="Total Amount" textFill="white">
                                                        <font>
                                                            <Font name="System Bold" size="20.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </HBox>
                                            <Label fx:id="priceLabel" text="160,000 VND" textFill="#ffd700" textAlignment="CENTER">
                                                <font>
                                                    <Font name="System Bold" size="24.0" />
                                                </font>
                                            </Label>
                                        </children>
                                    </VBox>

                                    <!-- Action Buttons -->
                                    <VBox spacing="15.0" style="-fx-padding: 10 0;">
                                        <children>
                                            <Button fx:id="payOrderBtn" mnemonicParsing="false" prefHeight="50.0" prefWidth="350.0" style="-fx-background-color: #ffd700; -fx-background-radius: 25; -fx-text-fill: #2c3e50; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 2);" text="💳 PAY ORDER">
                                                <font>
                                                    <Font name="System Bold" size="16.0" />
                                                </font>
                                                <cursor>
                                                    <Cursor fx:constant="HAND" />
                                                </cursor>
                                            </Button>
                                            <Button fx:id="cancelOrderBtn" mnemonicParsing="false" prefHeight="45.0" prefWidth="350.0" style="-fx-background-color: rgba(255,255,255,0.2); -fx-background-radius: 25; -fx-text-fill: white; -fx-border-color: white; -fx-border-radius: 25; -fx-border-width: 2;" text="❌ CANCEL ORDER">
                                                <font>
                                                    <Font name="System Bold" size="14.0" />
                                                </font>
                                                <cursor>
                                                    <Cursor fx:constant="HAND" />
                                                </cursor>
                                            </Button>
                                        </children>
                                    </VBox>
                                </children>
                            </VBox>
                        </children>
                    </HBox>
                </content>
            </ScrollPane>
        </center>
    </BorderPane>
   <AnchorPane fx:id="loadingOverlay" layoutX="10.0" layoutY="10.0" style="-fx-background-color: rgba(0,0,0,0.3);" visible="false" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
      <children>
         <ProgressIndicator fx:id="loadingProgress" minHeight="100.0" minWidth="100.0" AnchorPane.bottomAnchor="200.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="200.0" />
      </children>
   </AnchorPane>
</AnchorPane>
