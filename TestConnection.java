import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

public class TestConnection {
    public static void main(String[] args) {
        String[] passwords = {"", "postgres", "admin", "password", "123456"};
        String url = "*************************************";
        String user = "postgres";
        
        for (String password : passwords) {
            try {
                System.out.println("Trying password: '" + password + "'");
                Connection conn = DriverManager.getConnection(url, user, password);
                System.out.println("SUCCESS! Connected with password: '" + password + "'");
                conn.close();
                break;
            } catch (SQLException e) {
                System.out.println("Failed with password '" + password + "': " + e.getMessage());
            }
        }
    }
}
