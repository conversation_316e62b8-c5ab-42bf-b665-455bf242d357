<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.text.Font?>

<StackPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.BookScreen">
    <children>
        <AnchorPane prefHeight="200.0" prefWidth="200.0" style="-fx-background-color: dadada;">
            <children>
                <AnchorPane fx:id="loginForm" prefHeight="400.0" prefWidth="600.0">
                    <children>
                  <BorderPane layoutX="156.0" layoutY="40.0" prefHeight="348.0" prefWidth="288.0" style="-fx-background-color: fff; -fx-background-radius: 15;" />
                  <Label layoutX="164.0" layoutY="252.0" text="HardCover:">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_coverType" layoutX="286.0" layoutY="247.0" prefHeight="30.0" prefWidth="152.0" />
                  <Label layoutX="168.0" layoutY="71.0" text="Author:">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                        <TextField fx:id="book_author" layoutX="286.0" layoutY="67.0" prefHeight="30.0" prefWidth="152.0" />
                        <Button fx:id="addBookBtn" layoutX="288.0" layoutY="346.0" mnemonicParsing="false" onAction="#addBookBtnAction" text="Add" />
                  <DatePicker fx:id="book_publicationDate" layoutX="286.0" layoutY="138.0" prefHeight="30.0" prefWidth="152.0" />
                  <Label layoutX="166.0" layoutY="143.0" text="Publication date:">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <Label layoutX="165.0" layoutY="180.0" text="Language:">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_language" layoutX="286.0" layoutY="175.0" prefHeight="30.0" prefWidth="152.0" />
                  <Label layoutX="164.0" layoutY="216.0" text="Genre:">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_genre" layoutX="286.0" layoutY="211.0" prefHeight="30.0" prefWidth="152.0" />
                  <Label layoutX="166.0" layoutY="107.0" text="Pages">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_pages" layoutX="286.0" layoutY="103.0" prefHeight="30.0" prefWidth="50.0" />
                  <Label layoutX="164.0" layoutY="288.0" text="Publisher:">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="book_publisher" layoutX="286.0" layoutY="283.0" prefHeight="30.0" prefWidth="152.0" />
                        <Label fx:id="bookDetailLabel" layoutX="224.0" layoutY="14.0" text="Add Book detail">
                            <font>
                                <Font name="System Bold" size="20.0" />
                            </font>
                        </Label>
                    </children>
                </AnchorPane>
            </children>
        </AnchorPane>
    </children>
</StackPane>
