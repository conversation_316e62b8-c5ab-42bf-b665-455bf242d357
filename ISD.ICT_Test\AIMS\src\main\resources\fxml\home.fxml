<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import java.lang.*?>
<?import javafx.scene.layout.*?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: #D3D3D3;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <HBox prefHeight="104.0" prefWidth="1326.0" style="-fx-border-color: #33adff;">
         <children>
            <VBox prefHeight="102.0" prefWidth="231.0">
               <children>
                  <Pane prefHeight="102.0" prefWidth="237.0">
                     <children>
                        <ImageView fx:id="aimsImage" fitHeight="110.0" fitWidth="100.0" layoutX="50.0" pickOnBounds="true" preserveRatio="true" style="-fx-cursor: hand;">
                           <image>
                              <Image url="@logos/aim_logo.png" />
                           </image>
                        </ImageView>
                     </children>
                  </Pane>
               </children>
            </VBox>
            <HBox prefHeight="102.0" prefWidth="600.0">
               <children>
                  <Pane prefHeight="102.0" prefWidth="562.0">
                     <children>
                        <TextField fx:id="searchTextField" layoutX="17.0" layoutY="27.0" prefHeight="48.0" prefWidth="407.0" />
                     </children>
                  </Pane>
                  <Pane prefHeight="102.0" prefWidth="397.0">
                     <children>
                        <SplitMenuButton fx:id="splitMenuBtnSearch" layoutX="50.0" layoutY="27.0" mnemonicParsing="false" prefHeight="48.0" prefWidth="105.0" text="Search">
                          <items>
                            <MenuItem mnemonicParsing="false" text="Action 1" />
                            <MenuItem mnemonicParsing="false" text="Action 2" />
                          </items>
                        </SplitMenuButton>
                        <ChoiceBox fx:id="choiceBoxOrder" layoutX="180.0" layoutY="27.0" prefHeight="48.0" prefWidth="120.0" />
                     </children>
                  </Pane>
               </children>
            </HBox>
            <HBox prefHeight="102.0" prefWidth="600.0" spacing="15.0" alignment="CENTER_LEFT">
               <children>
                  <Button fx:id="orderButton" mnemonicParsing="false" prefHeight="50.0" prefWidth="100.0" text="ORDER" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-background-radius: 8; -fx-font-weight: bold;" />
                  <Button fx:id="cartButton" mnemonicParsing="false" prefHeight="50.0" prefWidth="100.0" text="CART" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-background-radius: 8; -fx-font-weight: bold;" />
                  <Button fx:id="accountButton" mnemonicParsing="false" prefHeight="50.0" prefWidth="100.0" text="ACCOUNT" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 8; -fx-font-weight: bold;" />
                  <VBox alignment="CENTER" spacing="5.0">
                     <children>
                        <Label fx:id="numMediaInCart" text="0 media" style="-fx-font-weight: bold; -fx-font-size: 14px;" />
                        <Label text="in cart" style="-fx-font-size: 12px; -fx-text-fill: #6c757d;" />
                     </children>
                  </VBox>
                  <HBox.margin>
                     <Insets left="10.0" right="10.0" />
                  </HBox.margin>
               </children>
            </HBox>
         </children>
      </HBox>
      <VBox layoutY="103.0" prefHeight="678.0" prefWidth="1326.0" style="-fx-background-color: #D3D3D3;">
         <children>
            <HBox fx:id="hboxMedia" layoutX="24.0" layoutY="123.0" prefHeight="600.0" prefWidth="1327.0" style="-fx-background-color: #D3D3D3;">
               <children>
                  <VBox fx:id="vboxMedia1" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia2" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia3" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia4" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia5" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
               </children>
            </HBox>
            <HBox alignment="CENTER" layoutX="24.0" layoutY="723.0" prefHeight="50.0" prefWidth="1327.0" spacing="10.0">
               <children>
                  <Button fx:id="btnPrevPage" alignment="CENTER" mnemonicParsing="false" prefHeight="30.0" prefWidth="76.0" text="Previous" />
                  <Label fx:id="lblPageInfo" alignment="CENTER" contentDisplay="CENTER" text="Page 1 of x" />
                  <Button fx:id="btnNextPage" mnemonicParsing="false" prefHeight="30.0" prefWidth="76.0" text="Next" />
               </children>
            </HBox>
         </children>
      </VBox>
   </children>
</AnchorPane>
