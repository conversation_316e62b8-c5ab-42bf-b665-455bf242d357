package com.hust.ict.aims;

import com.hust.ict.aims.utils.Configs;
import com.hust.ict.aims.view.home.HomeScreenHandler;
import javafx.stage.Stage;

public class GuestLoginTest {
    public static void main(String[] args) {
        System.out.println("Testing guest login functionality...");
        
        try {
            // Simulate what happens when "Continue as Guest" is clicked
            System.out.println("Creating new Stage...");
            Stage stage = new Stage();
            
            System.out.println("Creating HomeScreenHandler with path: " + Configs.HOME_PATH);
            HomeScreenHandler homeHandler = new HomeScreenHandler(stage, Configs.HOME_PATH);
            
            System.out.println("Setting screen title...");
            homeHandler.setScreenTitle("Home Screen");
            
            System.out.println("Guest login test completed successfully!");
            System.out.println("The HomeScreenHandler was created without errors.");
            System.out.println("This means the guest login functionality should work properly.");
            
        } catch (Exception e) {
            System.out.println("Guest login test failed with exception:");
            e.printStackTrace();
        }
    }
}
