<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.effect.DropShadow?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);" xmlns="http://javafx.com/javafx/20.0.1" xmlns:fx="http://javafx.com/fxml/1">
	<BorderPane prefHeight="788.0" prefWidth="1326.0">
		<top>
			<VBox style="-fx-background-color: white; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);" BorderPane.alignment="CENTER">
				<children>
					<HBox alignment="CENTER" prefHeight="100.0" style="-fx-padding: 15;">
						<children>
							<ImageView fx:id="aimsImage" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
								<image>
									<Image url="@../assets/images/Logo.png" />
								</image>
								<cursor>
									<Cursor fx:constant="HAND" />
								</cursor>
								<HBox.margin>
									<Insets right="20.0" />
								</HBox.margin>
							</ImageView>
							<Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="60.0" prefWidth="2.0" style="-fx-background-color: #dee2e6;" />
							<VBox alignment="CENTER_LEFT" spacing="5.0">
								<children>
									<Label text="RUSH DELIVERY" textFill="#dc3545">
										<font>
											<Font name="System Bold" size="28.0" />
										</font>
									</Label>
									<Label text="Express Shipping Information" textFill="#6c757d">
										<font>
											<Font size="16.0" />
										</font>
									</Label>
								</children>
								<HBox.margin>
									<Insets left="20.0" />
								</HBox.margin>
							</VBox>
							<Region HBox.hgrow="ALWAYS" />
						</children>
					</HBox>
				</children>
			</VBox>
		</top>
		<center>
			<ScrollPane fitToWidth="true" style="-fx-background-color: transparent;" BorderPane.alignment="CENTER">
				<content>
					<VBox prefWidth="1326.0" spacing="25.0" style="-fx-padding: 30;">
						<children>
							<!-- Rush Delivery Form -->
							<VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 30; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
								<children>
									<HBox alignment="CENTER_LEFT" spacing="10.0">
										<children>
											<Label text="⚡" textFill="#dc3545">
												<font>
													<Font size="28.0" />
												</font>
											</Label>
											<Label text="Rush Delivery Details" textFill="#2c3e50">
												<font>
													<Font name="System Bold" size="24.0" />
												</font>
											</Label>
										</children>
									</HBox>
									<Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

									<!-- Delivery Time Selection -->
									<VBox spacing="15.0">
										<children>
											<Label text="Desired Delivery Time *" textFill="#495057">
												<font>
													<Font name="System Bold" size="18.0" />
												</font>
											</Label>

											<HBox spacing="15.0" alignment="CENTER_LEFT">
												<children>
													<VBox spacing="5.0">
														<children>
															<Label text="Date" textFill="#6c757d">
																<font>
																	<Font name="System Bold" size="14.0" />
																</font>
															</Label>
															<HBox spacing="8.0">
																<children>
																	<ChoiceBox fx:id="dayChoiceBox" prefWidth="70.0" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da;" />
																	<ChoiceBox fx:id="monthChoiceBox" prefWidth="70.0" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da;" />
																	<ChoiceBox fx:id="yearChoiceBox" prefWidth="80.0" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da;" />
																</children>
															</HBox>
														</children>
													</VBox>

													<Separator orientation="VERTICAL" prefHeight="60.0" style="-fx-background-color: #e9ecef;" />

													<VBox spacing="5.0">
														<children>
															<Label text="Time" textFill="#6c757d">
																<font>
																	<Font name="System Bold" size="14.0" />
																</font>
															</Label>
															<HBox spacing="8.0">
																<children>
																	<ChoiceBox fx:id="hourChoiceBox" prefWidth="70.0" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da;" />
																	<Label text=":" textFill="#6c757d">
																		<font>
																			<Font size="18.0" />
																		</font>
																	</Label>
																	<ChoiceBox fx:id="minuteChoiceBox" prefWidth="70.0" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da;" />
																</children>
															</HBox>
														</children>
													</VBox>
												</children>
											</HBox>

											<Label text="⚠️ Rush delivery is available within 3 days from today" textFill="#ffc107" style="-fx-background-color: #fff3cd; -fx-background-radius: 8; -fx-padding: 10;">
												<font>
													<Font name="System Bold" size="12.0" />
												</font>
											</Label>
										</children>
									</VBox>

									<!-- Instructions Section -->
									<VBox spacing="10.0">
										<children>
											<Label text="Special Instructions" textFill="#495057">
												<font>
													<Font name="System Bold" size="18.0" />
												</font>
											</Label>
											<TextArea fx:id="instructionsField" prefHeight="120.0" promptText="Enter any special delivery instructions..." style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da; -fx-font-size: 14;" wrapText="true" />
										</children>
									</VBox>
								</children>
							</VBox>

							<!-- Action Buttons -->
							<HBox alignment="CENTER" spacing="20.0" style="-fx-padding: 20 0;">
								<children>
									<Button fx:id="cancelRushDeliveryButton" mnemonicParsing="false" prefHeight="50.0" prefWidth="120.0" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 8; -fx-font-weight: bold; -fx-font-size: 16;" text="Cancel">
										<cursor>
											<Cursor fx:constant="HAND" />
										</cursor>
										<effect>
											<DropShadow color="#6c757d" radius="5.0" />
										</effect>
									</Button>
									<Button fx:id="proceedRushDeliveryButton" mnemonicParsing="false" prefHeight="50.0" prefWidth="120.0" style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-background-radius: 8; -fx-font-weight: bold; -fx-font-size: 16;" text="Continue">
										<cursor>
											<Cursor fx:constant="HAND" />
										</cursor>
										<effect>
											<DropShadow color="#dc3545" radius="5.0" />
										</effect>
									</Button>
								</children>
							</HBox>

							<Label text="* Fields marked with an asterisk are mandatory" textFill="#6c757d" style="-fx-font-style: italic;">
								<font>
									<Font size="12.0" />
								</font>
							</Label>
						</children>
					</VBox>
				</content>
			</ScrollPane>
		</center>
	</BorderPane>
</AnchorPane>
