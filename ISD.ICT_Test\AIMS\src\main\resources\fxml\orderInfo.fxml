<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.effect.DropShadow?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.view.order.OrderHandler">
	<BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0">
		<top>
			<VBox style="-fx-background-color: white; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);" BorderPane.alignment="CENTER">
				<children>
					<HBox alignment="CENTER" prefHeight="100.0" prefWidth="200.0" style="-fx-padding: 15;">
						<children>
							<ImageView fx:id="aimsImage" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
								<Image url="@../assets/images/Logo.png" />
								<cursor>
									<Cursor fx:constant="HAND" />
								</cursor>
								<HBox.margin>
									<Insets right="20.0" />
								</HBox.margin>
							</ImageView>
							<Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="60.0" prefWidth="2.0" style="-fx-background-color: #dee2e6;" />
							<Label contentDisplay="CENTER" prefHeight="60.0" prefWidth="300.0" text="ORDER TRACKING" textAlignment="CENTER" textFill="#2c3e50">
								<font>
									<Font name="System Bold" size="28.0" />
								</font>
								<HBox.margin>
									<Insets left="20.0" />
								</HBox.margin>
							</Label>
							<Region HBox.hgrow="ALWAYS" />
						</children>
					</HBox>
				</children>
			</VBox>
		</top>
		<center>
			<ScrollPane fitToWidth="true" style="-fx-background-color: transparent;" BorderPane.alignment="CENTER">
				<content>
					<VBox prefWidth="1326.0" spacing="20.0" style="-fx-padding: 30;">
						<children>
							<!-- Search Section -->
							<VBox spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 25; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
								<children>
									<Label text="Find Your Order" textFill="#2c3e50">
										<font>
											<Font name="System Bold" size="24.0" />
										</font>
									</Label>
									<Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

									<GridPane hgap="20.0" vgap="15.0">
										<columnConstraints>
											<ColumnConstraints hgrow="SOMETIMES" minWidth="120.0" prefWidth="120.0" />
											<ColumnConstraints hgrow="SOMETIMES" minWidth="300.0" prefWidth="400.0" />
											<ColumnConstraints hgrow="SOMETIMES" minWidth="100.0" prefWidth="150.0" />
										</columnConstraints>
										<rowConstraints>
											<RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
											<RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
										</rowConstraints>
										<children>
											<Label text="Order ID:" textFill="#495057" GridPane.rowIndex="0">
												<font>
													<Font name="System Bold" size="16.0" />
												</font>
											</Label>
											<TextField fx:id="searchFieldOrderid" prefHeight="40.0" promptText="Enter your order ID" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="0" />

											<Label text="Email:" textFill="#495057" GridPane.rowIndex="1">
												<font>
													<Font name="System Bold" size="16.0" />
												</font>
											</Label>
											<TextField fx:id="searchFieldEmail" prefHeight="40.0" promptText="Enter your email address" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ced4da; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="1" />

											<Button fx:id="searchButton" mnemonicParsing="false" prefHeight="40.0" prefWidth="120.0" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-background-radius: 8; -fx-font-weight: bold; -fx-font-size: 14;" text="SEARCH" GridPane.columnIndex="2" GridPane.rowIndex="0" GridPane.rowSpan="2">
												<cursor>
													<Cursor fx:constant="HAND" />
												</cursor>
												<effect>
													<DropShadow color="#007bff" radius="5.0" />
												</effect>
											</Button>
										</children>
									</GridPane>

									<Label fx:id="noOrderFoundLabel" prefHeight="40.0" text="No orders found for this ID and email" textAlignment="CENTER" textFill="#dc3545" visible="false">
										<font>
											<Font name="System Bold" size="16.0" />
										</font>
									</Label>
								</children>
							</VBox>
							<!-- Order Details Section -->
							<HBox spacing="20.0" fx:id="orderDetailsContainer" visible="false">
								<children>
									<!-- Order Information Card -->
									<VBox prefWidth="600.0" spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 25; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
										<children>
											<HBox alignment="CENTER_LEFT" spacing="10.0">
												<children>
													<Label text="📋" textFill="#007bff">
														<font>
															<Font size="24.0" />
														</font>
													</Label>
													<Label text="Order Information" textFill="#2c3e50">
														<font>
															<Font name="System Bold" size="22.0" />
														</font>
													</Label>
												</children>
											</HBox>
											<Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

											<GridPane fx:id="gridPane" hgap="15.0" vgap="12.0">
												<columnConstraints>
													<ColumnConstraints hgrow="SOMETIMES" minWidth="140.0" prefWidth="140.0" />
													<ColumnConstraints hgrow="SOMETIMES" minWidth="300.0" prefWidth="400.0" />
												</columnConstraints>
												<rowConstraints>
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
													<RowConstraints minHeight="35.0" prefHeight="35.0" vgrow="SOMETIMES" />
												</rowConstraints>
												<children>
													<Label text="Order ID:" textFill="#6c757d" GridPane.rowIndex="0">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="orderIdField" text="123456" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="0">
														<font>
															<Font size="14.0" />
														</font>
													</Label>

													<Label text="Customer Name:" textFill="#6c757d" GridPane.rowIndex="1">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="recipientNameField" text="Alice" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="1">
														<font>
															<Font size="14.0" />
														</font>
													</Label>

													<Label text="Phone:" textFill="#6c757d" GridPane.rowIndex="2">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="phoneField" text="0123456789" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="2">
														<font>
															<Font size="14.0" />
														</font>
													</Label>

													<Label text="Email:" textFill="#6c757d" GridPane.rowIndex="3">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="emailField" text="<EMAIL>" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="3">
														<font>
															<Font size="14.0" />
														</font>
													</Label>

													<Label text="Address:" textFill="#6c757d" GridPane.rowIndex="4">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="addressField" text="1 Đại Cồ Việt, Hà Nội" textFill="#2c3e50" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="4">
														<font>
															<Font size="14.0" />
														</font>
													</Label>

													<Label text="Subtotal:" textFill="#6c757d" GridPane.rowIndex="5">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="subtotalField" text="100,000 VND" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="5">
														<font>
															<Font size="14.0" />
														</font>
													</Label>

													<Label text="Total:" textFill="#6c757d" GridPane.rowIndex="6">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="totalField" text="110,000 VND" textFill="#28a745" GridPane.columnIndex="1" GridPane.rowIndex="6">
														<font>
															<Font name="System Bold" size="16.0" />
														</font>
													</Label>

													<Label text="Status:" textFill="#6c757d" GridPane.rowIndex="7">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="statusField" text="Pending" textFill="#ffc107" style="-fx-background-color: #fff3cd; -fx-background-radius: 15; -fx-padding: 5 10;" GridPane.columnIndex="1" GridPane.rowIndex="7">
														<font>
															<Font name="System Bold" size="12.0" />
														</font>
													</Label>

													<Label fx:id="labelDelivery" text="Delivery Time:" textFill="#6c757d" GridPane.rowIndex="8">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="deliveryTimeField" text="12:00 PM" textFill="#2c3e50" GridPane.columnIndex="1" GridPane.rowIndex="8">
														<font>
															<Font size="14.0" />
														</font>
													</Label>

													<Label fx:id="labelInstruction" text="Instructions:" textFill="#6c757d" GridPane.rowIndex="9">
														<font>
															<Font name="System Bold" size="14.0" />
														</font>
													</Label>
													<Label fx:id="instructionField" text="Leave at door" textFill="#2c3e50" wrapText="true" GridPane.columnIndex="1" GridPane.rowIndex="9">
														<font>
															<Font size="14.0" />
														</font>
													</Label>
												</children>
											</GridPane>
										</children>
									</VBox>
									<!-- Order Items Card -->
									<VBox prefWidth="650.0" spacing="15.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 25; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
										<children>
											<HBox alignment="CENTER_LEFT" spacing="10.0">
												<children>
													<Label text="🛒" textFill="#28a745">
														<font>
															<Font size="24.0" />
														</font>
													</Label>
													<Label text="Order Items" textFill="#2c3e50">
														<font>
															<Font name="System Bold" size="22.0" />
														</font>
													</Label>
												</children>
											</HBox>
											<Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

											<TableView fx:id="tableOrderMedia" prefHeight="300.0" style="-fx-background-color: transparent; -fx-border-color: #e9ecef; -fx-border-radius: 8;">
												<columns>
													<TableColumn fx:id="col_id" prefWidth="50.0" text="ID" style="-fx-alignment: CENTER;" />
													<TableColumn fx:id="col_title" prefWidth="250.0" text="Product Title" />
													<TableColumn fx:id="col_unit_price" prefWidth="120.0" text="Unit Price" style="-fx-alignment: CENTER;" />
													<TableColumn fx:id="col_quantity" prefWidth="80.0" text="Qty" style="-fx-alignment: CENTER;" />
													<TableColumn fx:id="col_price" prefWidth="120.0" text="Total Price" style="-fx-alignment: CENTER;" />
												</columns>
											</TableView>

											<HBox alignment="CENTER" spacing="15.0">
												<children>
													<Button fx:id="cancelOrderBtn" mnemonicParsing="false" prefHeight="45.0" prefWidth="150.0" style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-background-radius: 8; -fx-font-weight: bold; -fx-font-size: 14;" text="Cancel Order">
														<cursor>
															<Cursor fx:constant="HAND" />
														</cursor>
														<effect>
															<DropShadow color="#dc3545" radius="5.0" />
														</effect>
													</Button>
												</children>
											</HBox>
										</children>
									</VBox>
								</children>
							</HBox>
						</children>
					</VBox>
				</content>
			</ScrollPane>
		</center>
	</BorderPane>
</AnchorPane>
