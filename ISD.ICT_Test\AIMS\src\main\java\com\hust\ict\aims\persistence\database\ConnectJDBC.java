package com.hust.ict.aims.persistence.database;

import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Properties;

public class ConnectJDBC {

    public static Connection getConnection() {
        Connection conn = null;

        try (InputStream input = ConnectJDBC.class.getClassLoader().getResourceAsStream("db.properties")) {
            // load the properties file
            Properties pros = new Properties();
            if (input == null) {
                System.out.println("Sorry, unable to find db.properties");
                return null;
            }
            pros.load(input);
            // assign db parameters
            String url = pros.getProperty("url");
            String user = pros.getProperty("user");
            String password = pros.getProperty("password");
            // create a connection to the database
            conn = DriverManager.getConnection(url, user, password);
            System.out.println("Connection successful !");
        } catch (IOException | SQLException e) {
            System.out.println(e.getMessage());
        }
        return conn;
    }
}

