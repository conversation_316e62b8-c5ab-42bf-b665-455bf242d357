<?xml version="1.0" encoding="UTF-8"?>

<?import java.lang.*?>
<?import javafx.scene.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.text.Font?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" style="-fx-background-color: white;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.ChangePasswordController">
   <children>
      <GridPane layoutX="50.0" layoutY="128.0" prefHeight="180.0" prefWidth="500.0">
        <columnConstraints>
          <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
          <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
        </columnConstraints>
        <rowConstraints>
            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
          <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
        </rowConstraints>
         <children>
            <Label prefHeight="45.0" prefWidth="250.0" text="Current Password:" GridPane.rowIndex="1">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            <Label prefHeight="45.0" prefWidth="250.0" text="New Password:" GridPane.rowIndex="2">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            <Label prefHeight="45.0" prefWidth="250.0" text="Confirm New Password:" GridPane.rowIndex="3">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            <PasswordField fx:id="curPassword" GridPane.columnIndex="1" GridPane.rowIndex="1" />
            <PasswordField fx:id="newPassword" GridPane.columnIndex="1" GridPane.rowIndex="2" />
            <PasswordField fx:id="confirmNewPassword" GridPane.columnIndex="1" GridPane.rowIndex="3" />
            <Label prefHeight="45.0" prefWidth="250.0" text="Your Account Email:">
               <font>
                  <Font size="16.0" />
               </font>
            </Label>
            <TextField fx:id="emailInput" GridPane.columnIndex="1" />
         </children>
      </GridPane>
      <Button fx:id="confirmBtn" layoutX="164.0" layoutY="335.0" mnemonicParsing="false" onAction="#confirmBtn" prefWidth="80.0" style="-fx-background-color: D3D3D3;" text="Confirm">
         <font>
            <Font size="16.0" />
         </font>
         <cursor>
            <Cursor fx:constant="HAND" />
         </cursor>
      </Button>
      <Button fx:id="cancelBtn" layoutX="368.0" layoutY="335.0" mnemonicParsing="false" onAction="#cancelBtn" prefWidth="80.0" style="-fx-background-color: D3D3D3;" text="Cancel">
         <font>
            <Font size="16.0" />
         </font>
         <cursor>
            <Cursor fx:constant="HAND" />
         </cursor>
      </Button>
      <Label layoutX="136.0" layoutY="24.0" text="Change Account Password">
         <font>
            <Font size="28.0" />
         </font>
      </Label>
      <Label fx:id="username" layoutX="281.0" layoutY="80.0" text="Label">
         <font>
            <Font size="16.0" />
         </font>
      </Label>
   </children>
</AnchorPane>
