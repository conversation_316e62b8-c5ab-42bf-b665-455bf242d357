<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.shape.*?>
<?import javafx.geometry.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import java.lang.*?>
<?import javafx.scene.layout.*?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: white;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0">
         <top>
            <HBox alignment="CENTER_LEFT" prefHeight="150.0" prefWidth="1326.0" BorderPane.alignment="CENTER">
               <children>
                  <ImageView fitHeight="150.0" fitWidth="180.0" pickOnBounds="true" preserveRatio="true">
                     <image>
                        <Image url="@logos/invoice_logo.png" />
                     </image></ImageView>
                  <Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="80.0" prefWidth="40.0" />
                  <Label contentDisplay="CENTER" prefHeight="86.0" prefWidth="318.0" text="INVOICE">
                     <font>
                        <Font size="34.0" />
                     </font>
                  </Label>
                  <Pane prefHeight="200.0" prefWidth="500.0" />
               </children>
            </HBox>
         </top>
         <center>
            <HBox prefHeight="458.0" spacing="40.0" style="-fx-background-color: #D3D3D3;" BorderPane.alignment="CENTER">
               <padding>
                  <Insets bottom="40.0" left="40.0" right="40.0" top="20.0" />
               </padding>
               <children>
                  <VBox prefWidth="800.0">
                     <children>
                        <Label layoutX="10.0" prefHeight="80.0" prefWidth="286.0" text="Delivery Information">
                           <font>
                              <Font size="31.0" />
                           </font>
                        </Label>
                        <GridPane prefHeight="200.0">
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="395.0" minWidth="10.0" prefWidth="169.0" />
                            <ColumnConstraints hgrow="SOMETIMES" maxWidth="631.0" minWidth="10.0" prefWidth="631.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints maxHeight="47.0" minHeight="10.0" prefHeight="47.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="56.0" minHeight="10.0" prefHeight="48.0" vgrow="SOMETIMES" />
                            <RowConstraints maxHeight="69.0" minHeight="0.0" prefHeight="49.0" vgrow="SOMETIMES" />
                              <RowConstraints maxHeight="78.0" minHeight="10.0" prefHeight="51.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label text="Recipient:" />
                              <Label text="Phone:" GridPane.rowIndex="1" />
                              <Label text="Address:" GridPane.rowIndex="2" />
                              <Label text="Email:" GridPane.rowIndex="3" />
                              <Label text="Bob" GridPane.columnIndex="1" />
                              <Label text="094542354" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                              <Label text="1 Đ. Độc Lập, Quán Thánh, Ba Đình, Hà Nội" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                              <Label text="<EMAIL>" GridPane.columnIndex="1" GridPane.rowIndex="3" />
                           </children>
                        </GridPane>
                        <HBox layoutY="400.0" prefHeight="46.0" prefWidth="800.0">
                           <children>
                              <Pane prefHeight="200.0" prefWidth="800.0" style="-fx-background-color: #F5F5F5; -fx-background-radius: 30;">
                                 <children>
                                    <Label layoutX="593.0" layoutY="13.0" prefWidth="200.0" text="Total" />
                                    <Label layoutX="25.0" layoutY="13.0" prefWidth="280.0" text="Media" />
                                    <Label layoutX="324.0" layoutY="13.0" prefWidth="150.0" text="Unit price" />
                                    <Label layoutX="493.0" layoutY="13.0" prefWidth="100.0" text="Quantity" />
                                 </children>
                              </Pane>
                           </children>
                        </HBox>
                        <ScrollPane prefHeight="200.0" prefWidth="200.0">
                           <content>
                              <VBox prefHeight="200.0" prefWidth="800.0" />
                           </content>
                        </ScrollPane>
                     </children>
                  </VBox>
                  <VBox prefHeight="200.0" prefWidth="300.0" style="-fx-background-color: #87CEEB; -fx-background-radius: 30;">
                     <children>
                        <Label alignment="CENTER" lineSpacing="20.0" prefWidth="300.0" text="Payment Information" textAlignment="CENTER" textFill="WHITE">
                           <font>
                              <Font size="25.0" />
                           </font>
                           <VBox.margin>
                              <Insets top="20.0" />
                           </VBox.margin>
                        </Label>
                        <Separator prefHeight="0.0">
                           <VBox.margin>
                              <Insets bottom="20.0" left="10.0" right="10.0" top="20.0" />
                           </VBox.margin></Separator>
                        <GridPane>
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label contentDisplay="CENTER" text="   Subtotal:">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label text="50.000VND" GridPane.columnIndex="1">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label text="   VAT" GridPane.rowIndex="1">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label text="5.000VND" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label text="   Shipping fee" GridPane.rowIndex="2">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label text="20.000VND" GridPane.columnIndex="1" GridPane.rowIndex="2">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                           </children>
                        </GridPane>
                        <Separator prefWidth="200.0">
                           <VBox.margin>
                              <Insets bottom="20.0" left="10.0" right="10.0" top="20.0" />
                           </VBox.margin>
                        </Separator>
                        <GridPane>
                          <columnConstraints>
                            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                            <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                          </columnConstraints>
                          <rowConstraints>
                            <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                          </rowConstraints>
                           <children>
                              <Label text="   Price:">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                              <Label text="111.400VND" GridPane.columnIndex="1">
                                 <font>
                                    <Font size="20.0" />
                                 </font>
                              </Label>
                           </children>
                        </GridPane>
                        <Button alignment="CENTER" contentDisplay="CENTER" mnemonicParsing="false" prefWidth="144.0" style="-fx-background-color: #228B22;" text="Pay Order" textFill="#e2ff09">
                           <font>
                              <Font size="20.0" />
                           </font>
                           <VBox.margin>
                              <Insets left="85.0" top="70.0" />
                           </VBox.margin>
                        </Button>
                        <Button alignment="CENTER" contentDisplay="CENTER" mnemonicParsing="false" style="-fx-background-color: #CD5C5C;" text="Cancel Order" textFill="#e2ff09">
                           <font>
                              <Font size="20.0" />
                           </font>
                           <VBox.margin>
                              <Insets left="85.0" top="40.0" />
                           </VBox.margin>
                        </Button>
                     </children>
                  </VBox>
               </children>
            </HBox>
         </center>
      </BorderPane>
   </children>
</AnchorPane>
