INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('admin', '1234', '<EMAIL>', true);

INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('user', '1234', '<EMAIL>', false);

INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('user2', '1234', '<EMAIL>', false);

INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('user3', '1234', '<EMAIL>', false);


-- Insert data into Media table
INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (200000, 'Sample Book', 100, 1, 'This is a sample book', '2022-01-01', true, 'sample_book.jpg', '12x6x9', '12345');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (150000, 'Sample CD', 50, 0.25, 'This is a sample CD', '2022-01-01', false, 'sample_cd.jpg', '15x6x9', '56789');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Sample DVD', 75, 0.25, 'This is a sample DVD', '2022-01-01', true, 'sample_dvd.jpg', '17x6x9', '98465');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (120000, 'Sugar Baby', 30, 1, 'Best of 2024 book', '2024-03-03', true, 'book2.jpg', '20x24x8', '124312');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (80000, 'Ambition Monster', 50, 1.25, 'Best of 2024 Fiction book', '2024-08-11', false, 'book3.jpg', '20x24x8', '4312');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (145000, 'The Fetishist', 18, 2, 'New one', '2024-10-02', true, 'book4.jpg', '20x24x5', '122123');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (200000, 'Come & Get It', 60, 0.87, 'Best seller 2024', '2024-10-02', false, 'book5.jpg', '24x24x5', '110023');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (233000, 'WORRY', 45, 1.22, 'Book seller of 2024', '2024-10-12', true, 'book6.jpg', '20x24x8', '1221');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (175000, 'Blue Sisters', 40, 1.2, 'Book seller of 2024', '2024-10-10', true, 'book7.jpg', '20x24x8', '12212');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (30000, 'Best of Blue', 25, 0.56, 'Blue Music CD', '2024-10-12', true, 'cd1.jpg', '20x24x2', '12213');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (30000, 'The Dreaming', 30, 0.66, 'Fall in dreaming space', '2024-10-12', true, 'cd2.png', '20x24x2', '12214');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (130000, 'The Fame Monster', 35, 0.6, 'Fall in Lady Gaga new release', '2024-10-12', true, 'cd3.jpg', '20x24x2', '12215');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (140000, 'The Pink Print', 30, 0.6, 'Nicki Minaj New Album', '2024-10-12', true, 'cd4.jpg', '20x24x2', '12216');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (140000, 'The Album', 32, 0.6, 'The First Full Album of BlackPink', '2024-10-10', false, 'cd5.jpg', '20x24x2', '12218');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (70000, 'Square One: Boombayah', 35, 0.66, 'New Single of BlackPink', '2024-10-10', false, 'cd6.jpg', '20x24x2', '122123');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (120000, 'Ferrari', 40, 0.66, 'FERRARI NEWEST FILM', '2024-10-10', true, 'dvd1.jpg', '20x24x2', '82123');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (220000, 'BeeKeeper', 49, 0.66, 'Hottest block', '2024-10-09', true, 'dvd2.jpg', '20x24x2', '8223');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (210000, 'The Image Of You', 55, 0.66, 'Hottest block', '2024-10-09', true, 'dvd3.jpg', '20x24x2', '82232');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (240000, 'Killers of Flower Moon', 90, 0.56, 'Hottest film in 2024', '2024-09-09', true, 'dvd4.jpg', '20x24x2', '82234');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (160000, 'Perfect Days', 87, 0.56, 'Healing film', '2024-09-09', true, 'dvd5.jpg', '20x24x2', '82237');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (1850000, 'A Regular Woman', 94, 0.6, 'Best of 2022', '2024-09-09', true, 'dvd6.jpg', '20x24x2', '8223477');

-- Insert data into Book table
INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (1, 'Author Name', 'Hardcover', 'Publisher Name', '2022-01-01', 300, 'English', 'Fiction');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (4, 'Celine SaintClaire', 'Hardcover', 'BIO', '2022-01-01', 300, 'English', 'Romantic');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (5, 'Jenifer Romolini', 'Hardcover', 'Amazon', '2022-01-01', 350, 'English', 'Novel');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (6, 'Katherin Min', 'Hardcover', 'PIMP', '2022-01-01', 440, 'English', 'Comedy');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (7, 'Kiley Rey', 'Hardcover', 'Amazon', '2022-01-01', 500, 'English', 'Comedy');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (8, 'Alexandra Tanner', 'Hardcover', 'BIO', '2022-01-01', 300, 'English', 'Novel');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (9, 'Coco Mellers', 'Hardcover', 'PIMP', '2022-01-01', 500, 'English', 'Fiction');

-- Insert data into CD_and_LP table
INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (2, true, 'None of Them', 'Record Label', 'Track 1, Track 2, Track 3', 'Pop', '2022-01-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (10, true, 'Blue Genre', 'Record Label', 'Track 1, Track 2, Track 3', 'Pop', '2022-03-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (11, true, 'Kate Bush', 'Record Label', 'Track 1, Track 2, Track 3', 'Pop', '2022-02-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (12, true, 'Lada Gaga', 'Universal Record', 'Track 1, Track 2, Track 3', 'Pop', '2022-06-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (13, false, 'Nicki Minaj', 'Universal Record', 'Track 1, Track 2, Track 3', 'Pop & Rap', '2022-05-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (14, true, 'BlackPink', 'YG', 'Track 1, Track 2, Track 3', 'KPop', '2023-10-04');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (15, false, 'BlackPink', 'YG', 'Track 1, Track 2, Track 3', 'KPop', '2022-01-01');

-- Insert data into DVD table
INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (3, 'Movie', 'Steven Spielberg', 120, 'Universal Pictures', 'English', 'English', '1993-06-11', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (16, 'Movie', 'Steven Spielberg', 145, 'Universal Pictures', 'English', 'English', '2003-06-10', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (17, 'Movie', 'Leo', 140, 'Universal Pictures', 'English', 'English', '2018-09-12', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (18, 'Movie', 'MPCA', 130, 'MPCA', 'English', 'English', '2020-10-12', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (19, 'Movie', 'Martin Scorsese', 135, 'Universal Pictures', 'English', 'English', '2018-10-01', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (20, 'Movie', 'Wim Wenders', 170, 'Universal Pictures', 'English', 'Vietnamese', '2023-06-11', 'Romantic');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (21, 'Movie', 'Sherry Hormann', 160, 'Universal Pictures', 'English', 'Korean', '2022-06-11', 'Novel');


-- Insert data into DeliveryInfo table
INSERT INTO DeliveryInfo (name, phone, email, province, address, message) 
VALUES ('John Doe', '0123456789', '<EMAIL>', 'Hanoi', '123 ABC Street', 'Do not drop it');

-- Insert data into OrderInfo table
INSERT INTO OrderInfo (shippingFees, subtotal, status, delivery_id) VALUES (100, 500, 'PENDING', 1);
INSERT INTO OrderInfo (shippingFees, subtotal, status, delivery_id) VALUES (666, 666, 'ACCEPTED', 1);

-- Insert data into RushOrderInfo table
INSERT INTO RushOrderInfo (deliveryTime, instruction, order_id) VALUES ('2022-12-31 23:59:59', 'Handle with care', 1);


-- Insert data into Order_Media table
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (1, 1, 3, 0);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (1, 2, 2, 1);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (1, 3, 7, 0);

INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (2, 1, 9, 0);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (2, 2, 9, 0);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (2, 3, 9, 0);