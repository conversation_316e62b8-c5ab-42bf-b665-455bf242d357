<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import javafx.geometry.Insets?>

<AnchorPane prefHeight="160.0" prefWidth="260.0" style="-fx-background-color: white; -fx-border-color: #e0e0e0; -fx-border-radius: 8; -fx-background-radius: 8; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);" xmlns="http://javafx.com/javafx/19" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <ImageView fx:id="mediaImage" fitHeight="132.0" fitWidth="99.0" layoutX="14.0" layoutY="14.0" pickOnBounds="true" preserveRatio="true" style="-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.2), 3, 0, 0, 0);">
         <image>
            <Image url="@../assets/images/1.png" />
         </image>
      </ImageView>
      <Pane layoutX="135.0" layoutY="0.0" prefHeight="160.0" prefWidth="125.0">
         <children>
            <VBox prefHeight="160.0" prefWidth="125.0" spacing="5">
               <padding>
                  <Insets top="8" right="5" bottom="5" left="5"/>
               </padding>
               <children>
                  <Label fx:id="mediaTitle" alignment="CENTER" prefHeight="30.0" prefWidth="115.0" text="Title" textAlignment="CENTER" wrapText="true" style="-fx-text-fill: #2c3e50;">
                     <font>
                        <Font name="System Bold" size="14.0" />
                     </font>
                  </Label>
                  
                  <HBox alignment="CENTER_LEFT" prefHeight="24.0" prefWidth="115.0" style="-fx-background-color: #f8f9fa; -fx-background-radius: 4;">
                     <padding>
                        <Insets left="5" right="5"/>
                     </padding>
                     <children>
                        <Label alignment="CENTER_RIGHT" prefHeight="20.0" prefWidth="45.0" text="Price:" style="-fx-text-fill: #3498db;">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                        <Label fx:id="mediaPrice" prefHeight="20.0" prefWidth="65.0" text="100" style="-fx-text-fill: #2c3e50;">
                           <font>
                              <Font size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </HBox>
                  
                  <HBox alignment="CENTER_LEFT" prefHeight="24.0" prefWidth="115.0" style="-fx-background-color: #f8f9fa; -fx-background-radius: 4;">
                     <padding>
                        <Insets left="5" right="5"/>
                     </padding>
                     <children>
                        <Label alignment="CENTER_RIGHT" prefHeight="20.0" prefWidth="45.0" text="Avail:" style="-fx-text-fill: #3498db;">
                           <font>
                              <Font name="System Bold Italic" size="13.0" />
                           </font>
                        </Label>
                        <Label fx:id="mediaAvail" prefHeight="20.0" prefWidth="65.0" text="9" style="-fx-text-fill: #2c3e50;">
                           <font>
                              <Font size="13.0" />
                           </font>
                        </Label>
                     </children>
                  </HBox>
                  
                  <Spinner fx:id="spinnerChangeNumber" prefHeight="25.0" prefWidth="115.0" style="-fx-background-radius: 4;" />
                  
                  <Button fx:id="addToCartBtn" alignment="CENTER" mnemonicParsing="false" prefHeight="30.0" prefWidth="115.0" style="-fx-cursor: hand; -fx-background-color: #3498db; -fx-text-fill: white; -fx-background-radius: 4;" text="Add to Cart">
                     <font>
                        <Font name="System Bold" size="12.0" />
                     </font>
                  </Button>
               </children>
            </VBox>
         </children>
      </Pane>
   </children>
</AnchorPane>
