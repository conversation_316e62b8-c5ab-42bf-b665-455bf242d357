<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.text.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.effect.*?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="700.0" prefWidth="1000.0" style="-fx-background-color: linear-gradient(to bottom, #667eea 0%, #764ba2 100%);" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <!-- Left Panel with Logo -->
      <AnchorPane prefHeight="700.0" prefWidth="500.0" style="-fx-background-color: rgba(255, 255, 255, 0.1); -fx-background-radius: 0 20 20 0;">
         <children>
            <VBox alignment="CENTER" layoutX="50.0" layoutY="150.0" prefHeight="400.0" prefWidth="400.0" spacing="30.0">
               <children>
                  <ImageView fitHeight="200.0" fitWidth="200.0" pickOnBounds="true" preserveRatio="true">
                     <image>
                        <Image url="@logos/aim_logo.png" />
                     </image>
                     <effect>
                        <DropShadow blurType="GAUSSIAN" color="rgba(0,0,0,0.3)" offsetX="2.0" offsetY="2.0" radius="10.0" />
                     </effect>
                  </ImageView>
                  <Label text="WELCOME TO AIMS" textFill="WHITE" style="-fx-font-weight: bold;">
                     <font>
                        <Font name="System Bold" size="36.0" />
                     </font>
                     <effect>
                        <DropShadow blurType="GAUSSIAN" color="rgba(0,0,0,0.5)" offsetX="1.0" offsetY="1.0" radius="5.0" />
                     </effect>
                  </Label>
                  <Label text="Academic Information Management System" textFill="rgba(255,255,255,0.9)" textAlignment="CENTER" wrapText="true">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>
               </children>
            </VBox>
         </children>
      </AnchorPane>

      <!-- Right Panel with Login Form -->
      <AnchorPane layoutX="500.0" prefHeight="700.0" prefWidth="500.0">
         <children>
            <VBox alignment="CENTER" layoutX="50.0" layoutY="100.0" prefHeight="500.0" prefWidth="400.0" spacing="25.0" style="-fx-background-color: white; -fx-background-radius: 20; -fx-padding: 40;">
               <effect>
                  <DropShadow blurType="GAUSSIAN" color="rgba(0,0,0,0.2)" offsetX="0.0" offsetY="5.0" radius="15.0" />
               </effect>
               <children>
                  <Label text="LOGIN" style="-fx-text-fill: #333333; -fx-font-weight: bold;">
                     <font>
                        <Font name="System Bold" size="32.0" />
                     </font>
                  </Label>

                  <VBox spacing="20.0" prefWidth="320.0">
                     <children>
                        <VBox spacing="8.0">
                           <children>
                              <Label text="Username" style="-fx-text-fill: #555555; -fx-font-weight: bold;">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <TextField fx:id="username" prefHeight="45.0" promptText="Enter your username" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ddd; -fx-border-width: 1; -fx-padding: 10; -fx-font-size: 14;">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </TextField>
                           </children>
                        </VBox>

                        <VBox spacing="8.0">
                           <children>
                              <Label text="Password" style="-fx-text-fill: #555555; -fx-font-weight: bold;">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </Label>
                              <PasswordField fx:id="password" prefHeight="45.0" promptText="Enter your password" style="-fx-background-radius: 8; -fx-border-radius: 8; -fx-border-color: #ddd; -fx-border-width: 1; -fx-padding: 10; -fx-font-size: 14;">
                                 <font>
                                    <Font size="14.0" />
                                 </font>
                              </PasswordField>
                           </children>
                        </VBox>
                     </children>
                  </VBox>

                  <Button fx:id="loginBtn" prefHeight="50.0" prefWidth="320.0" text="LOGIN" style="-fx-background-color: linear-gradient(to right, #667eea, #764ba2); -fx-text-fill: white; -fx-background-radius: 25; -fx-font-weight: bold; -fx-cursor: hand;">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                     <effect>
                        <DropShadow blurType="GAUSSIAN" color="rgba(102,126,234,0.4)" offsetX="0.0" offsetY="3.0" radius="8.0" />
                     </effect>
                  </Button>

                  <VBox alignment="CENTER" spacing="15.0">
                     <children>
                        <Hyperlink fx:id="homeHyperLink" text="Continue as Guest" style="-fx-text-fill: #667eea; -fx-font-weight: bold;">
                           <font>
                              <Font size="14.0" />
                           </font>
                        </Hyperlink>
                        <Hyperlink fx:id="forgotPasswordLink" text="Forgot Password?" style="-fx-text-fill: #764ba2;">
                           <font>
                              <Font size="12.0" />
                           </font>
                        </Hyperlink>
                     </children>
                  </VBox>
               </children>
            </VBox>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
