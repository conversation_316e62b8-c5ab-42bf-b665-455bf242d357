package com.hust.ict.aims;

import com.hust.ict.aims.persistence.dao.user.UserDAO;
import com.hust.ict.aims.persistence.dao.media.MediaDAO;
import com.hust.ict.aims.entity.user.User;
import com.hust.ict.aims.entity.media.Media;
import java.util.List;

public class DatabaseContentTest {
    public static void main(String[] args) {
        System.out.println("Testing database content...");
        
        try {
            // Test Users
            UserDAO userDAO = new UserDAO();
            List<User> users = userDAO.getAll();
            System.out.println("Number of users in database: " + users.size());
            
            for (User user : users) {
                System.out.println("User: " + user.getUsername() + 
                                 ", Email: " + user.getEmail() + 
                                 ", Admin: " + user.getIsAdmin());
            }
            
            // Test Media
            MediaDAO mediaDAO = new MediaDAO();
            List<Media> mediaList = mediaDAO.getAllMedia();
            System.out.println("Number of media items in database: " + mediaList.size());
            
            if (mediaList.size() > 0) {
                System.out.println("Sample media items:");
                for (int i = 0; i < Math.min(5, mediaList.size()); i++) {
                    Media media = mediaList.get(i);
                    System.out.println("Media: " + media.getTitle() + 
                                     ", Type: " + media.getMediaTypeName() + 
                                     ", Price: " + media.getPrice());
                }
            }
            
        } catch (Exception e) {
            System.out.println("Database content test failed with exception:");
            e.printStackTrace();
        }
    }
}
