<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="640.0" prefWidth="950.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <VBox layoutX="14.0" layoutY="14.0" prefHeight="611.0" prefWidth="337.0">
         <children>
            <Label prefHeight="183.0" prefWidth="340.0" text="Welcome, product manager">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Label text="What do you want to do?">
               <font>
                  <Font size="24.0" />
               </font>
            </Label>
            <Button mnemonicParsing="false" text="Back" />
         </children>
      </VBox>
      <Label layoutX="568.0" layoutY="108.0" text="Manage Account">
         <font>
            <Font size="24.0" />
         </font>
      </Label>
      <Button layoutX="603.0" layoutY="401.0" mnemonicParsing="false" text="Change Password" />
      <TextField layoutX="658.0" layoutY="308.0" />
      <TextField layoutX="658.0" layoutY="227.0" />
      <Label layoutX="558.0" layoutY="231.0" text="New Password" />
      <Label layoutX="512.0" layoutY="311.0" text="Confirm New Password" />
   </children>
</AnchorPane>
