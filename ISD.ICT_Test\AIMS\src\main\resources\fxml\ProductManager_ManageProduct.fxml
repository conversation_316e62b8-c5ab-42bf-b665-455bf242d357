<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>


<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="640.0" prefWidth="950.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <VBox layoutX="14.0" layoutY="14.0" prefHeight="611.0" prefWidth="337.0">
         <children>
            <Label prefHeight="183.0" prefWidth="340.0" text="Welcome, product manager">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Label text="What do you want to do?">
               <font>
                  <Font size="24.0" />
               </font>
            </Label>
            <Button mnemonicParsing="false" text="Back" />
         </children>
      </VBox>
      <Button layoutX="634.0" layoutY="216.0" mnemonicParsing="false" text="Book" />
      <Label layoutX="568.0" layoutY="108.0" text="Manage Product">
         <font>
            <Font size="24.0" />
         </font>
      </Label>
      <Button layoutX="631.0" layoutY="320.0" mnemonicParsing="false" text="CD/LP" />
      <Button layoutX="636.0" layoutY="422.0" mnemonicParsing="false" text="DVD" />
   </children>
</AnchorPane>
