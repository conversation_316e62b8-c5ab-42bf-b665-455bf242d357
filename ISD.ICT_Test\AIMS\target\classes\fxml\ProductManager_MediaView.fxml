<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>


<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="640.0" prefWidth="950.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <VBox layoutX="14.0" layoutY="14.0" prefHeight="611.0" prefWidth="337.0">
         <children>
            <Label prefHeight="183.0" prefWidth="340.0" text="Welcome, product manager">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Label text="What do you want to do?">
               <font>
                  <Font size="24.0" />
               </font>
            </Label>
            <Button mnemonicParsing="false" text="Back" />
         </children>
      </VBox>
      <TableView fx:id="medias_tableView" layoutX="351.0" layoutY="25.0" maxWidth="1.7976931348623157E308" onMouseClicked="#selectMedia" prefHeight="276.0" prefWidth="590.0">
         <columns>
            <TableColumn fx:id="medias_col_id" prefWidth="46.0" text="ID" />
            <TableColumn fx:id="medias_col_title" prefWidth="94.0" text="Title" />
            <TableColumn fx:id="medias_col_category" minWidth="2.0" prefWidth="67.99993896484375" text="Category" />
            <TableColumn fx:id="medias_col_price" prefWidth="65.3333740234375" text="Price" />
            <TableColumn fx:id="medias_col_quantity" prefWidth="25.99993896484375" text="Qtt" />
            <TableColumn fx:id="medias_col_importDate" prefWidth="77.33331298828125" text="Import date" />
            <TableColumn fx:id="medias_col_desciption" prefWidth="171.3333740234375" text="Description" />
         </columns>
         <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
         </columnResizePolicy>
      </TableView>
      <Button layoutX="475.0" layoutY="356.0" mnemonicParsing="false" text="Add" />
      <Button layoutX="594.0" layoutY="356.0" mnemonicParsing="false" text="Update" />
      <Button layoutX="738.0" layoutY="356.0" mnemonicParsing="false" text="Delete" />
   </children>
</AnchorPane>
