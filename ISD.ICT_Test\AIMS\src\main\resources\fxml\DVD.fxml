<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.text.Font?>

<StackPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.DVDScreen">
    <children>
        <AnchorPane prefHeight="200.0" prefWidth="200.0" style="-fx-background-color: 555555;">
            <children>
                <AnchorPane fx:id="loginForm" prefHeight="400.0" prefWidth="601.0" style="-fx-background-color: dadada;">
                    <children>
                  <BorderPane layoutX="154.0" layoutY="37.0" prefHeight="355.0" prefWidth="293.0" style="-fx-background-color: ffffff; -fx-background-radius: 15;" />
                        <Button fx:id="addDVDBtn" layoutX="281.0" layoutY="360.0" mnemonicParsing="false" onAction="#addDVDBtnAction" text="Add" />
                        <Label fx:id="DVDDetailLabel" layoutX="227.0" layoutY="7.0" text="Add DVD detail">
                            <font>
                                <Font name="System Bold" size="20.0" />
                            </font>
                        </Label>
                        <DatePicker fx:id="dvd_releasedDate" layoutX="301.0" layoutY="124.0" prefHeight="30.0" prefWidth="131.0" AnchorPane.rightAnchor="170.0" />
                        <Label layoutX="191.0" layoutY="128.0" text="Released date:" AnchorPane.leftAnchor="191.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <Label layoutX="194.0" layoutY="208.0" text="Language:" AnchorPane.leftAnchor="194.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="dvd_language" layoutX="301.0" layoutY="204.0" prefHeight="30.0" prefWidth="131.0" AnchorPane.rightAnchor="170.0" />
                        <Label layoutX="193.0" layoutY="286.0" text="Subtitles:" AnchorPane.leftAnchor="193.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="dvd_subtitles" layoutX="301.0" layoutY="282.0" prefHeight="30.0" prefWidth="131.0" AnchorPane.rightAnchor="170.0" />
                        <Label layoutX="193.0" layoutY="326.0" text="Runtime:" AnchorPane.leftAnchor="193.0" AnchorPane.topAnchor="326.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="dvd_runtime" layoutX="300.8" layoutY="321.4" prefHeight="30.0" prefWidth="50.0" AnchorPane.leftAnchor="300.8" AnchorPane.topAnchor="321.4" />
                        <Label layoutX="191.0" layoutY="88.0" text="DVD type:" AnchorPane.leftAnchor="191.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="dvd_type" layoutX="301.0" layoutY="84.0" prefHeight="30.0" prefWidth="131.0" AnchorPane.leftAnchor="301.0" />
                        <Label layoutX="193.0" layoutY="168.0" text="Director:" AnchorPane.leftAnchor="193.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="dvd_director" layoutX="301.0" layoutY="164.0" prefHeight="30.0" prefWidth="131.0" AnchorPane.leftAnchor="301.0" />
                        <Label layoutX="193.0" layoutY="247.0" text="Studio:" AnchorPane.leftAnchor="193.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="dvd_studio" layoutX="301.0" layoutY="243.0" prefHeight="30.0" prefWidth="131.0" AnchorPane.leftAnchor="301.0" />
                  <Label layoutX="192.0" layoutY="52.0" text="Film genre:" AnchorPane.leftAnchor="192.0" AnchorPane.topAnchor="52.0">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <TextField fx:id="dvd_genre" layoutX="301.0" layoutY="44.0" prefHeight="30.0" prefWidth="131.0" AnchorPane.leftAnchor="301.0" AnchorPane.topAnchor="44.0" />
                    </children>
                </AnchorPane>
            </children>
        </AnchorPane>
    </children>
</StackPane>
