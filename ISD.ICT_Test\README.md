# ISD.ICT.20232-10

This is the source code for the AIMS capstone project for the ITSS course

## Member 
| Student ID  | Name               | Role    |
|:------------|:-------------------|:--------|
| 20210451    | <PERSON><PERSON><PERSON>   | Leader  |
| 20200270    | <PERSON><PERSON>      | Member  |
| 20215210    | <PERSON><PERSON>      | Member  |
| 20215209    | <PERSON><PERSON><PERSON>   | Member  |
| 20215206    | <PERSON>g V<PERSON> Hoang    | Member  |

## Report content 

<details>
  <summary>W11: 20/05/2024-26/05/2024 </summary>
<br>
<details>
<summary><PERSON><PERSON><PERSON> Hu<PERSON></summary>
<br>

- Assigned tasks:
    - Create and Refractor source code
    - Add database configs
    - Add entity classes

- Implementation details:
    - Pull Request(s):[#1](https://github.com/NTHuyne/ISD.ICT.20232.10/pull/1)
    - Specific implementation details:
        - Implement entities classes for Place Order use cases
        - Implement and set up the configs for MySQL database

</details>

<details>
<summary><PERSON><PERSON></summary>
<br>

- Assigned tasks:
    - Implement View Cart use case

- Implementation details:
    - Pull Request(s): [#4](https://github.com/NTHuyne/ISD.ICT.20232.10/pull/4) 
    - Specific implementation details:
        - Implement controller, fxml view and handler for View Cart use case

</details>

<details>
<summary>Dang Ngoc Huy</summary>
<br>

- Assigned tasks:
    - Implement Home Screen and add to cart 

- Implementation details:
    - Pull Request(s): [#4](https://github.com/NTHuyne/ISD.ICT.20232.10/pull/4)
    - Specific implementation details:
        - Implement controller, fxml view and handler for Home Screen and Add To Cart, Search and Filter Media use cases.

</details>

<details>
<summary>Dang Viet Hoang</summary>
<br>

- Assigned tasks:
    - Implement VNPay Subsystem

- Implementation details:
    - Pull Request(s): [#2](https://github.com/NTHuyne/ISD.ICT.20232.10/pull/2)
    - Specific implementation details:
        - Implement subsystem interface: IPayment, IClient interface
        - Implement subsystem for VNPay
        - Implement test case for VNPay

</details>

</details>

<details>
  <summary>W12: 27/05/2024-02/06/2024 </summary>
<br>
<details>
<summary>Nguyen Trong Huy</summary>
<br>

- Assigned tasks:
    - Redesign the interface for shipping, invoice, cart.
    - Fix bugs in home screen

- Implementation details:
    - Pull Request(s):[#10](https://github.com/NTHuyne/ISD.ICT.20232.10/pull/10)
    - Specific implementation details:
        - Using SceneBuilder redesign shipping.fxml, invoice.fxml, rush_delivery_invoice.fxml.

</details>

<details>
<summary>Duong Van Huu</summary>
<br>

- Assigned tasks:
    - Writng report about Design Concepts

- Implementation details:
    - Writing report on Design Concepts

</details>

<details>
<summary>Dang Ngoc Huy</summary>
<br>

- Assigned tasks:
    - Writing report on SOLID principles

- Implementation details:
    - Writing report on SOLID principles

</details>

<details>
<summary>Dang Viet Hoang</summary>
<br>

- Assigned tasks:
    - Writing Design documents and report on design concepts

- Implementation details:
    - Writing documents

</details>

<details>
<summary>Nguyen Chan Hung</summary>
<br>

- Assigned tasks:
    - Implement shipping delivery, place order, place rush order.

- Implementation details:
    - Pull Request(s):[#10](https://github.com/NTHuyne/ISD.ICT.20232.10/pull/10)

</details>

</details>

<details>
  <summary>W13: 02/06/2024-08/06/2024 </summary>
<br>
<details>
<summary>Nguyen Trong Huy</summary>
<br>

- Assigned tasks:
    - Implement Login function

</details>

<details>
<summary>Duong Van Huu</summary>
<br>

- Assigned tasks:
    - Implement dashboard screen for product manager and CRUD products, viewing order

</details>

<details>
<summary>Dang Ngoc Huy</summary>
<br>

- Assigned tasks:
    - Implement dashboard screen for product manager and CRUD products, viewing order

</details>

<details>
<summary>Dang Viet Hoang</summary>
<br>

- Assigned tasks:
    - Complete connect VNPay to PayOrder

</details>

<details>
<summary>Nguyen Chan Hung</summary>
<br>

- Assigned tasks:
    - Complete Rush Delivery Form and Phone Validation

</details>

</details>

<details>
  <summary>W14 & W15: 08/06/2024-18/06/2024 </summary>
<br>
<details>
<summary>Nguyen Trong Huy</summary>
<br>

- Assigned tasks:
    - Email subsystem & integrate
    - Design-level class diagram
    - Control all documentations
    - Fix bugs
    - View Order Screen
    - Customer: review order by order id and email
    - Admin dashboard

</details>

<details>
<summary>Duong Van Huu</summary>
<br>

- Assigned tasks:
    - Customer: review order by order id and email
    - SRS documentation
    - Slides

</details>

<details>
<summary>Dang Ngoc Huy</summary>
<br>

- Assigned tasks:
    - Design-level class diagram
    - Fix bug for image of media 

</details>

<details>
<summary>Dang Viet Hoang</summary>
<br>

- Assigned tasks:
    - ERD & DB Schema
    - Issue redesign database for order
    - Product manager: update order status

</details>

<details>
<summary>Nguyen Chan Hung</summary>
<br>

- Assigned tasks:
    - Admin dashboard
    - Sequence diagram & Architecture design
    - Slides

</details>

</details>

## Sprint Backlog & Team Contribution 
![alt text][def]

[def]: ./Sprint%20Backlog%20&%20Team%20Contribution/Sprint%20Backlog%20&%20Team%20Contribution.png


## Demo Video

Link: https://drive.google.com/drive/folders/1qOB3Y8jajPrHn66cvP5pvTJ5lXbWPjY9?usp=sharing