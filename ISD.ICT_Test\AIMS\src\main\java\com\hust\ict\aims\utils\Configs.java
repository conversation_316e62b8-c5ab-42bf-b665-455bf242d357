package com.hust.ict.aims.utils;

import javafx.scene.text.Font;
import javafx.scene.text.FontPosture;
import javafx.scene.text.FontWeight;

public class Configs {

	public static String CURRENCY = "VND";
	public static float PERCENT_VAT = 10;

	// static resource
	public static final String IMAGE_PATH = "/assets/images";
	public static final String CART_SCREEN_PATH = "/fxml/cart.fxml";
	public static final String INVOICE_SCREEN_PATH = "/fxml/invoice.fxml";
	public static final String SHIPPING_SCREEN_PATH = "/fxml/shipping.fxml";
	public static final String RUSH_ORDER_SHIPPING_SCREEN_PATH = "/fxml/rush_order_shipping.fxml";
	public static final String CART_MEDIA_PATH = "/fxml/media_cart.fxml";
	public static final String HOME_PATH  = "/fxml/home.fxml";
	public static final String HOME_MEDIA_PATH = "/fxml/media_home.fxml";
	public static final String HOME_MEDIA_DETAIL_PATH = "/fxml/media_home_detail.fxml";
	public static final String INVOICE_MEDIA_PATH = "/fxml/media_invoice.fxml";
	public static final String RUSH_DELIVERY_INVOICE_PATH = "/fxml/rush_delivery_invoice.fxml";
	public static final String LOGIN_PATH = "/fxml/login.fxml";
	public static final String PRODUCT_MANAGER_PATH = "/fxml/productmanager.fxml";
	public static final String PRODUCT_MANAGER_MEDIA_PATH = "/fxml/productmanager_medias.fxml";
	public static final String PRODUCT_MANAGER_ORDER_PATH = "/fxml/productmanager_order.fxml";
	public static final String ORDER_SCREEN_PATH = "/fxml/orderInfo.fxml";
	public static final String ADMIN_PATH = "/fxml/admin.fxml";
	public static final String CHANGE_PASSWORD_PATH = "/fxml/changepasswordpm.fxml";



	public static Font REGULAR_FONT = Font.font("Segoe UI", FontWeight.NORMAL, FontPosture.REGULAR, 24);

	public static String[] PROVINCES = { "Bắc Giang", "Bắc Kạn", "Cao Bằng", "Hà Giang", "Lạng Sơn", "Phú Thọ",
			"Quảng Ninh", "Thái Nguyên", "Tuyên Quang", "Yên Bái", "Điện Biên", "Hòa Bình", "Lai Châu", "Sơn La",
			"Bắc Ninh", "Hà Nam", "Hải Dương", "Hưng Yên", "Nam Định", "Ninh Bình", "Thái Bình", "Vĩnh Phúc", "Hà Nội",
			"Hải Phòng", "Hà Tĩnh", "Nghệ An", "Quảng Bình", "Quảng Trị", "Thanh Hóa", "Thừa Thiên-Huế", "Đắk Lắk",
			"Đắk Nông", "Gia Lai", "Kon Tum", "Lâm Đồng", "Bình Định", "Bình Thuận", "Khánh Hòa", "Ninh Thuận",
			"Phú Yên", "Quảng Nam", "Quảng Ngãi", "Đà Nẵng", "Bà Rịa-Vũng Tàu", "Bình Dương", "Bình Phước", "Đồng Nai",
			"Tây Ninh", "Hồ Chí Minh", "An Giang", "Bạc Liêu", "Bến Tre", "Cà Mau", "Đồng Tháp", "Hậu Giang",
			"Kiên Giang", "Long An", "Sóc Trăng", "Tiền Giang", "Trà Vinh", "Vĩnh Long", "Cần Thơ" };
}
