<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.effect.Light.Distant?>
<?import javafx.scene.effect.Lighting?>
<?import javafx.scene.effect.Shadow?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Pane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.Cursor?>
<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" prefHeight="200.0" prefWidth="1139.0" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1">
<BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="129.0" prefWidth="1152.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1">
    <center>
    <HBox fx:id="hboxMedia" alignment="CENTER_LEFT" prefHeight="100.0" prefWidth="1139.0" style="-fx-background-color: fff; -fx-background-radius: 20px;">
        <children>
<!--            <Pane prefHeight="100.0" prefWidth="70.0" />-->
            <ImageView fx:id="image" fitHeight="100.0" fitWidth="81.0" pickOnBounds="true" preserveRatio="true">

            </ImageView>
            <Label fx:id="title" alignment="TOP_LEFT" contentDisplay="CENTER" prefHeight="100.0" prefWidth="300.0"  textAlignment="CENTER">
                <padding>
                    <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
                </padding>
                <font>
                    <Font name="SVN-Linux Libertine" size="18.0" />
                </font>
            </Label>
            <Label fx:id="price" prefHeight="23.0" prefWidth="180.0" text="1,200,300" textAlignment="CENTER">
                <font>
                    <Font name="SVN-Linux Libertine bold" size="18.0" />
                </font>
            </Label>
            <Label fx:id="quantity" prefHeight="23.0" prefWidth="180.0" text="5" textAlignment="CENTER">
                <font>
                    <Font name="SVN-Linux Libertine bold" size="18.0" />
                </font>
            </Label>
            <Button fx:id="deleteBtn" alignment="CENTER" prefHeight="18.0" prefWidth="130.0" text="Delete" textAlignment="CENTER" textFill="#ff3c3c">
                <font>
                    <Font size="14.0" />
                </font>
                <cursor>
                    <Cursor fx:constant="HAND" />
                </cursor>
            </Button>
        </children>
        <opaqueInsets>
            <Insets />
        </opaqueInsets>
        <VBox.margin>
            <Insets bottom="5.0" top="15.0" />
        </VBox.margin>
    </HBox>

<!--        </children>-->
    </center>
</BorderPane>
</AnchorPane>