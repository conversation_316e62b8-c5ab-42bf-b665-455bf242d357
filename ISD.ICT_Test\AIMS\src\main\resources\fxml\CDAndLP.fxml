<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.DatePicker?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.RadioButton?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.control.ToggleGroup?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.text.Font?>

<StackPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="400.0" prefWidth="600.0" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.controller.productmanager.CDAndLPScreen">
    <children>
        <AnchorPane prefHeight="200.0" prefWidth="200.0">
            <children>
                <AnchorPane fx:id="loginForm" layoutX="-1.0" prefHeight="400.0" prefWidth="601.0" style="-fx-background-color: dadada;">
                    <children>
                  <BorderPane layoutX="166.0" layoutY="60.0" prefHeight="329.0" prefWidth="275.0" style="-fx-background-color: ffffff; -fx-background-radius: 15;" />
                        <Button fx:id="addCDAndLPBtn" layoutX="282.0" layoutY="340.0" mnemonicParsing="false" onAction="#addCDAndLPBtnAction" text="Add" />
                        <Label fx:id="CDAndLPDetailLabel" layoutX="220.0" layoutY="24.0" text="Add CD/LP detail">
                            <font>
                                <Font name="System Bold" size="20.0" />
                            </font>
                        </Label>
                        <DatePicker fx:id="cdAndLp_releaseDate" layoutX="312.0" layoutY="116.0" prefHeight="30.0" prefWidth="107.0" />
                        <Label layoutX="190.0" layoutY="121.0" text="Publication date">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <Label layoutX="190.0" layoutY="197.0" text="Music genre">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_genre" layoutX="312.0" layoutY="192.0" prefHeight="30.0" prefWidth="107.0" />
                  <HBox alignment="CENTER" layoutX="11.0" layoutY="262.0" prefHeight="42.0" prefWidth="278.0" spacing="20.0" AnchorPane.bottomAnchor="95.60000000000002" AnchorPane.leftAnchor="11.0" AnchorPane.rightAnchor="10.599999999999966" AnchorPane.topAnchor="262.0">
                     <children>
                        <RadioButton fx:id="cdAndLp_isCD" mnemonicParsing="false" selected="true" text="CD" HBox.hgrow="ALWAYS">
                           <font>
                              <Font size="15.0" />
                           </font>
                           <toggleGroup>
                              <ToggleGroup fx:id="CDorLP" />
                           </toggleGroup>
                        </RadioButton>
                        <RadioButton fx:id="cdAndLp_isLP" mnemonicParsing="false" text="LP" toggleGroup="$CDorLP" HBox.hgrow="ALWAYS">
                           <font>
                              <Font size="15.0" />
                           </font>
                        </RadioButton>
                     </children>
                  </HBox>
                        <Label layoutX="190.0" layoutY="82.0" text="Artists" AnchorPane.leftAnchor="190.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_artists" layoutX="312.0" layoutY="77.0" prefHeight="30.0" prefWidth="107.0" />
                        <Label layoutX="190.0" layoutY="160.0" text="Record label" AnchorPane.leftAnchor="190.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_recordLabel" layoutX="312.0" layoutY="155.0" prefHeight="30.0" prefWidth="107.0" />
                        <Label layoutX="190.0" layoutY="235.0" text="Track list" AnchorPane.leftAnchor="190.0">
                            <font>
                                <Font size="15.0" />
                            </font>
                        </Label>
                        <TextField fx:id="cdAndLp_trackList" layoutX="312.0" layoutY="230.0" prefHeight="30.0" prefWidth="107.0" />
                    </children>
                </AnchorPane>
            </children>
        </AnchorPane>
    </children>
</StackPane>
