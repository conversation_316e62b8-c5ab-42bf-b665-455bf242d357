package com.hust.ict.aims.controller.productmanager;

/*
<PERSON><PERSON><PERSON> của MediaScreen
- <PERSON><PERSON> (Polymorphism): MediaScreen cho phép các đối tượng của các lớp <PERSON> (như BookScreen và CDScreen)
được xử lý thông qua một giao diện chung.
- Mở Rộng Dễ Dàng: <PERSON>hi cần thêm một loại media mới, bạn chỉ cần tạo một lớp mới thực hiện MediaScreen mà không cần
sửa đổi code hiện tại.
- Loose Coupling: <PERSON>ệ thống trở nên ít phụ thuộc vào các lớp cụ thể, gi<PERSON><PERSON> dễ dàng bảo trì và mở rộng.
* */

public interface MediaScreen {
    void showScreen();
}
