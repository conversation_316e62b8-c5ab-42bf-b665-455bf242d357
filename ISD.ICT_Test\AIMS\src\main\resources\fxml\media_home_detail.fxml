<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<?import javafx.geometry.Insets?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="500.0" prefWidth="700.0" style="-fx-background-color: #f5f5f5;" xmlns="http://javafx.com/javafx/17.0.2-ea" xmlns:fx="http://javafx.com/fxml/1">
    <children>
        <VBox alignment="TOP_CENTER" spacing="15" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0" AnchorPane.bottomAnchor="0.0">
            <padding>
                <Insets top="20" right="20" bottom="20" left="20"/>
            </padding>
            <children>
                <Label fx:id="detailedMediaTitle" alignment="CENTER" contentDisplay="CENTER" prefHeight="40.0" prefWidth="600.0" text="Media Title" textAlignment="CENTER" style="-fx-text-fill: #2c3e50;">
                    <font>
                        <Font name="System Bold" size="32.0" />
                    </font>
                </Label>
                
                <HBox spacing="30" alignment="CENTER">
                    <VBox alignment="TOP_CENTER" spacing="10">
                        <ImageView fx:id="detailedMediaImage" fitHeight="300.0" fitWidth="220.0" pickOnBounds="true" preserveRatio="true" style="-fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.3), 10, 0, 0, 0);">
                        </ImageView>
                    </VBox>
                    
                    <VBox spacing="8" prefHeight="300.0" prefWidth="400.0" style="-fx-background-color: white; -fx-background-radius: 10; -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 5);">
                        <padding>
                            <Insets top="15" right="15" bottom="15" left="15"/>
                        </padding>
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label text="Price:" prefWidth="100.0" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedMediaPrice" prefHeight="20.0" prefWidth="250.0" text="100" style="-fx-text-fill: #2c3e50;" />
                        </HBox>
                        
                        <Separator />
                        
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label text="Category:" prefWidth="100.0" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedMediaCategory" prefHeight="20.0" prefWidth="250.0" text="Book" style="-fx-text-fill: #2c3e50;" />
                        </HBox>
                        
                        <Separator />
                        
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label text="Description:" prefWidth="100.0" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedMediaDescription" prefHeight="20.0" prefWidth="250.0" text="Abc" style="-fx-text-fill: #2c3e50;" wrapText="true" />
                        </HBox>
                        
                        <Separator />
                        
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label text="Rush Order:" prefWidth="100.0" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedMediaRushOrder" prefHeight="20.0" prefWidth="250.0" text="Yes" style="-fx-text-fill: #2c3e50;" />
                        </HBox>
                        
                        <Separator />
                        
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label fx:id="info1" prefWidth="100.0" text="info1" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedInfo1" prefHeight="20.0" prefWidth="250.0" text="Abc" style="-fx-text-fill: #2c3e50;" />
                        </HBox>
                        
                        <Separator />
                        
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label fx:id="info2" prefWidth="100.0" text="info2" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedInfo2" prefHeight="20.0" prefWidth="250.0" text="Yes" style="-fx-text-fill: #2c3e50;" />
                        </HBox>
                        
                        <Separator />
                        
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label fx:id="info3" prefWidth="100.0" text="info3" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedInfo3" prefHeight="20.0" prefWidth="250.0" text="Yes" style="-fx-text-fill: #2c3e50;" />
                        </HBox>
                        
                        <Separator />
                        
                        <HBox alignment="CENTER_LEFT" prefHeight="35.0" styleClass="detail-row">
                            <Label fx:id="info4" prefWidth="100.0" text="info4" style="-fx-font-weight: bold; -fx-text-fill: #3498db;">
                                <font>
                                    <Font name="System Bold" size="14.0" />
                                </font>
                            </Label>
                            <Label fx:id="detailedInfo4" prefHeight="20.0" prefWidth="250.0" text="Yes" style="-fx-text-fill: #2c3e50;" />
                        </HBox>
                    </VBox>
                </HBox>
            </children>
        </VBox>
    </children>
</AnchorPane>
