package com.hust.ict.aims;

import com.hust.ict.aims.persistence.database.ConnectJDBC;
import java.sql.Connection;

public class DatabaseConnectionTest {
    public static void main(String[] args) {
        System.out.println("Testing database connection...");
        
        try {
            Connection conn = ConnectJDBC.getConnection();
            if (conn != null) {
                System.out.println("Database connection successful!");
                System.out.println("Connection URL: " + conn.getMetaData().getURL());
                System.out.println("Database Product: " + conn.getMetaData().getDatabaseProductName());
                System.out.println("Database Version: " + conn.getMetaData().getDatabaseProductVersion());
                conn.close();
            } else {
                System.out.println("Database connection failed - connection is null");
            }
        } catch (Exception e) {
            System.out.println("Database connection failed with exception:");
            e.printStackTrace();
        }
    }
}
