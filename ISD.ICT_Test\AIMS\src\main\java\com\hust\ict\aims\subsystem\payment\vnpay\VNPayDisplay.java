package com.hust.ict.aims.subsystem.payment.vnpay;

import java.awt.Desktop;
import java.io.IOException;
import java.net.URI;
import net.freeutils.httpserver.HTTPServer;
import javafx.application.Platform;

public class VNPayDisplay {
	private VNPayOrderManager orderManager;

	public VNPayDisplay(VNPayOrderManager orderManager) {
		super();
		this.orderManager = orderManager;
	}

	private void displayURL(String url) {
        try {
        	// Try JavaFX HostServices first (if available)
        	Platform.runLater(() -> {
        		try {
        			// Use AWT Desktop as fallback
        			if (Desktop.isDesktopSupported()) {
        				Desktop desk = Desktop.getDesktop();
        				if (desk.isSupported(Desktop.Action.BROWSE)) {
        					desk.browse(new URI(url));
        				} else {
        					System.err.println("Browser action not supported");
        				}
        			} else {
        				System.err.println("Desktop not supported");
        			}
        		} catch (Exception e) {
        			System.err.println("Failed to open browser: " + e.getMessage());
        			e.printStackTrace();
        		}
        	});
		} catch (Exception e) {
			System.err.println("Failed to open payment URL: " + e.getMessage());
			e.printStackTrace();
		}
	}
	
	private void startURLReceivingServer() {
		final HTTPServer server = new URLReceivingServer().build(orderManager);

		try {
			server.start();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public void sendPayOrder(String queryURL) {
		displayURL(queryURL);
		startURLReceivingServer();
	}

	public void sendPayOrder(String queryURL, IBrowserDisplay browser) {
		browser.displayURL(queryURL);
		startURLReceivingServer();
	}
}
