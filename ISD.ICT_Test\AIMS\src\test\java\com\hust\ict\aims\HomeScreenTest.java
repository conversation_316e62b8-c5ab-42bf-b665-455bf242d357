package com.hust.ict.aims;

import com.hust.ict.aims.utils.Configs;
import com.hust.ict.aims.view.home.HomeScreenHandler;
import javafx.application.Application;
import javafx.stage.Stage;

public class HomeScreenTest extends Application {
    
    @Override
    public void start(Stage primaryStage) {
        System.out.println("Starting HomeScreen test...");
        
        try {
            System.out.println("Creating HomeScreenHandler...");
            HomeScreenHandler homeHandler = new HomeScreenHandler(primaryStage, Configs.HOME_PATH);
            System.out.println("HomeScreenHandler created successfully!");
            
            homeHandler.setScreenTitle("Home Screen Test");
            System.out.println("Screen title set successfully!");
            
            homeHandler.show();
            System.out.println("HomeScreen shown successfully!");
            
        } catch (Exception e) {
            System.out.println("Error occurred while creating HomeScreen:");
            e.printStackTrace();
        }
    }
    
    public static void main(String[] args) {
        System.out.println("Launching HomeScreen test application...");
        launch(args);
    }
}
